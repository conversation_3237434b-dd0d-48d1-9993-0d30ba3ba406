#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新术语表的脚本
将新术语添加到AI翻译prompt.txt中
"""

import json
import re
from pathlib import Path

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return None

def create_english_mapping(api_name):
    """根据API名称创建英文术语"""
    # 移除TFT15_前缀和其他前缀
    name = api_name.replace('TFT15_', '').replace('TFT_', '').replace('TFT11_', '')
    
    # 处理一些特殊映射
    special_mappings = {
        'Destroyer': 'Destroyer',
        'HonorStudent': 'Honor Student', 
        'InfernalSpeed': 'Infernal Speed',
        'Unstoppable': 'Unstoppable',
        'DragonLord': 'Dragon Lord',
        'MagicSpecialist': 'Magic Specialist',
        'Mastermind': 'Mastermind',
        '<PERSON>lord': 'Edgelord',
        'CrystalRose': '<PERSON> Rose',
        'BestDefense': 'Best Defense',
        'MaxVitality': 'Max Vitality',
        'Unremarkable': 'Unremarkable',
        'Colossus': 'Colossus',
        'Connector': 'Connector',
        'MaxAttack': 'Max Attack',
        'ProudStance': 'Proud Stance',
        'GoldenEdge': 'Golden Edge',
        'Vitality': 'Vitality',
        'Unyielding': 'Unyielding',
        'ExtendedArm': 'Extended Arm',
        'BloodBrothers': 'Blood Brothers',
        'HeroicArc': 'Heroic Arc',
        'DogPile': 'Dog Pile',
        'ChaosRising': 'Chaos Rising',
        'GoldenTouch': 'Golden Touch',
        'Sniper': 'Sniper',
        'StarGuardian': 'Star Guardian',
        'Mage': 'Mage',
        'SolarFlare': 'Solar Flare',
        'Precision': 'Precision',
        'FrostLord': 'Frost Lord',
        'OneHundredPushups': '100 Pushups',
        'GoldenHeart': 'Golden Heart',
        'GodzillaTank': 'Godzilla Tank',
        'WarriorSpirit': 'Warrior Spirit',
        'SpaceAce': 'Space Ace',
        'Encumbered': 'Encumbered',
        'Heavyweight': 'Heavyweight',
        'SpikeArmor': 'Spike Armor',
        'HighEnergy': 'High Energy',
        'Odyssey': 'Odyssey',
        'MechPilot': 'Mech Pilot',
        'StormStrike': 'Storm Strike',
        'BreakingThrough': 'Breaking Through',
        'GatheringQi': 'Gathering Qi',
        'KillerInstinct': 'Killer Instinct',
        'Atomic': 'Atomic',
        'SelfishSelfish': 'Selfish',
        'SoloCarry': 'Solo Carry',
        'StandAlone': 'Stand Alone',
        'Soldier': 'Soldier',
        'NewGamePlus': 'New Game Plus',
        'PowerLevel9000': 'Power Level 9000',
        'MaxSpeed': 'Max Speed',
        'SharpEye': 'Sharp Eye',
        'FightingSpirit': 'Fighting Spirit',
        'BladeStorm': 'Blade Storm',
        'MagicalTwins': 'Magical Twins',
        'HatTrick': 'Hat Trick',
        'Superstar': 'Superstar',
        'MaxMana': 'Max Mana',
        'UltimateAscension': 'Ultimate Ascension',
        'LivingWall': 'Living Wall',
        'BlinkStrike': 'Blink Strike',
        'ManaOverflow': 'Mana Overflow',
        'PoroHeart': 'Poro Heart',
        'RareTreat': 'Rare Treat',
        'PowerSurge66': 'Power Surge 66',
        'FinalForm': 'Final Form',
        'Professional': 'Professional',
        'ExtraBloom': 'Extra Bloom',
        'TakeOff': 'Take Off'
    }
    
    # 如果有特殊映射，使用它
    if name in special_mappings:
        return special_mappings[name]
    
    # 否则使用原始名称
    return name

def get_trait_mappings():
    """获取羁绊的英文-中文映射"""
    traits_data = load_json_file('processed_data/tft_traits_zh_cn.json')
    if not traits_data or 'data' not in traits_data:
        return {}
    
    mappings = {}
    for trait_data in traits_data['data'].values():
        api_name = trait_data.get('apiName', '')
        chinese_name = trait_data.get('name', '')
        if api_name and chinese_name:
            english_name = create_english_mapping(api_name)
            mappings[chinese_name] = english_name
    
    return mappings

def update_traits_section():
    """更新羁绊部分"""
    # 加载新术语
    with open('new_terms_extracted.json', 'r', encoding='utf-8') as f:
        new_terms = json.load(f)
    
    # 获取羁绊映射
    trait_mappings = get_trait_mappings()
    
    # 读取现有术语表
    with open('processed_data/AI翻译prompt.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到羁绊部分
    trait_section_start = content.find('# 羁绊 (Traits)')
    trait_section_end = content.find('\n# ', trait_section_start + 1)
    
    if trait_section_start == -1:
        print("找不到羁绊部分")
        return
    
    # 提取现有羁绊部分
    existing_traits_section = content[trait_section_start:trait_section_end]
    
    # 创建新的羁绊条目
    new_trait_entries = []
    for chinese_name in new_terms['new_traits']:
        english_name = trait_mappings.get(chinese_name, chinese_name)
        new_trait_entries.append(f"{english_name}|{chinese_name}")
    
    # 合并现有和新的羁绊
    updated_traits_section = existing_traits_section.rstrip() + '\n'
    for entry in new_trait_entries:
        updated_traits_section += entry + '\n'
    
    # 替换内容
    updated_content = content[:trait_section_start] + updated_traits_section + content[trait_section_end:]
    
    # 保存更新后的文件
    with open('processed_data/AI翻译prompt.txt', 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✅ 已添加 {len(new_trait_entries)} 个新羁绊术语")

def get_item_mappings():
    """获取装备的英文-中文映射"""
    items_data = load_json_file('processed_data/tft_items_zh_cn.json')
    if not items_data or 'data' not in items_data:
        return {}

    mappings = {}
    for item_data in items_data['data'].values():
        api_name = item_data.get('apiName', '')
        chinese_name = item_data.get('name', '')
        if api_name and chinese_name:
            # 简化API名称作为英文名称
            english_name = api_name.replace('TFT_Item_', '').replace('TFT15_', '').replace('TFT11_', '').replace('TFT6_', '')
            # 处理一些特殊情况
            if chinese_name and not chinese_name.startswith('@') and not chinese_name.startswith('tft'):
                mappings[chinese_name] = english_name

    return mappings

def update_items_section():
    """更新装备部分"""
    # 加载新术语
    with open('new_terms_extracted.json', 'r', encoding='utf-8') as f:
        new_terms = json.load(f)

    # 获取装备映射
    item_mappings = get_item_mappings()

    # 读取现有术语表
    with open('processed_data/AI翻译prompt.txt', 'r', encoding='utf-8') as f:
        content = f.read()

    # 找到辅助装备部分的结尾
    support_items_end = content.find('Zz\'Rot Portal|兹若特传送门')
    if support_items_end == -1:
        print("找不到辅助装备部分结尾")
        return

    # 找到该行的结尾
    line_end = content.find('\n', support_items_end)
    if line_end == -1:
        line_end = len(content)

    # 创建新的装备条目
    new_item_entries = []
    for chinese_name in new_terms['new_items']:
        english_name = item_mappings.get(chinese_name, chinese_name.replace(' ', '_'))
        new_item_entries.append(f"{english_name}|{chinese_name}")

    # 添加新装备部分
    new_items_section = '\n\n# 新增装备 (New Items)\n'
    for entry in new_item_entries:
        new_items_section += entry + '\n'

    # 插入新内容
    updated_content = content[:line_end] + new_items_section + content[line_end:]

    # 保存更新后的文件
    with open('processed_data/AI翻译prompt.txt', 'w', encoding='utf-8') as f:
        f.write(updated_content)

    print(f"✅ 已添加 {len(new_item_entries)} 个新装备术语")

def get_augment_mappings():
    """获取强化符文的英文-中文映射"""
    augments_data = load_json_file('processed_data/tft_augments_zh_cn.json')
    if not augments_data or 'data' not in augments_data:
        return {}

    mappings = {}
    for augment_data in augments_data['data'].values():
        api_name = augment_data.get('apiName', '')
        chinese_name = augment_data.get('name', '')
        if api_name and chinese_name:
            # 简化API名称作为英文名称
            english_name = api_name.replace('TFT15_Augment_', '').replace('TFT11_Augment_', '').replace('TFT_Augment_', '')
            mappings[chinese_name] = english_name

    return mappings

def update_augments_section():
    """更新强化符文部分"""
    # 加载新术语
    with open('new_terms_extracted.json', 'r', encoding='utf-8') as f:
        new_terms = json.load(f)

    # 获取强化符文映射
    augment_mappings = get_augment_mappings()

    # 读取现有术语表
    with open('processed_data/AI翻译prompt.txt', 'r', encoding='utf-8') as f:
        content = f.read()

    # 找到强化符文部分的结尾
    augments_end = content.find('Young and Wild and Free|不拘一格')
    if augments_end == -1:
        print("找不到强化符文部分结尾")
        return

    # 找到该行的结尾
    line_end = content.find('\n', augments_end)
    if line_end == -1:
        line_end = len(content)

    # 创建新的强化符文条目
    new_augment_entries = []
    for chinese_name in new_terms['new_augments']:
        english_name = augment_mappings.get(chinese_name, chinese_name.replace(' ', '_'))
        new_augment_entries.append(f"{english_name}|{chinese_name}")

    # 添加新强化符文
    new_augments_text = '\n'
    for entry in new_augment_entries:
        new_augments_text += entry + '\n'

    # 插入新内容
    updated_content = content[:line_end] + new_augments_text + content[line_end:]

    # 保存更新后的文件
    with open('processed_data/AI翻译prompt.txt', 'w', encoding='utf-8') as f:
        f.write(updated_content)

    print(f"✅ 已添加 {len(new_augment_entries)} 个新强化符文术语")

def main():
    """主函数"""
    print("🔄 开始更新强化符文术语...")
    update_augments_section()
    print("✅ 强化符文术语更新完成")

if __name__ == "__main__":
    main()
