#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取新术语的脚本
从测试服数据中提取新的弈子、羁绊、装备、强化符文等术语
"""

import json
import re
from pathlib import Path

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return None

def extract_existing_terms(prompt_file):
    """从现有术语表中提取已有术语"""
    existing_terms = {
        'champions': set(),
        'traits': set(), 
        'items': set(),
        'augments': set()
    }
    
    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取弈子术语
        champion_section = re.search(r'# 弈子 \(Champions\)(.*?)(?=# |$)', content, re.DOTALL)
        if champion_section:
            for line in champion_section.group(1).split('\n'):
                if '|' in line and not line.strip().startswith('#'):
                    parts = line.split('|')
                    if len(parts) >= 2:
                        chinese_name = parts[1].strip()
                        if chinese_name:
                            existing_terms['champions'].add(chinese_name)
        
        # 提取羁绊术语
        trait_section = re.search(r'# 羁绊 \(Traits\)(.*?)(?=# |$)', content, re.DOTALL)
        if trait_section:
            for line in trait_section.group(1).split('\n'):
                if '|' in line and not line.strip().startswith('#'):
                    parts = line.split('|')
                    if len(parts) >= 2:
                        chinese_name = parts[1].strip()
                        if chinese_name:
                            existing_terms['traits'].add(chinese_name)
        
        # 提取装备术语 (包括光明武器、可合成装备、奥恩神器等)
        item_sections = [
            r'# 光明武器 \(Radiant Items\)(.*?)(?=# |$)',
            r'# 可合成装备 \(Craftable Items\)(.*?)(?=# |$)',
            r'# 奥恩神器 \(Ornn Artifacts\)(.*?)(?=# |$)',
            r'# 辅助装备 \(Support Items\)(.*?)(?=# |$)'
        ]
        
        for pattern in item_sections:
            section = re.search(pattern, content, re.DOTALL)
            if section:
                for line in section.group(1).split('\n'):
                    if '|' in line and not line.strip().startswith('#'):
                        parts = line.split('|')
                        if len(parts) >= 2:
                            chinese_name = parts[1].strip()
                            if chinese_name:
                                existing_terms['items'].add(chinese_name)
        
        # 提取强化符文术语
        augment_section = re.search(r'# 强化符文 \(Augments\)(.*?)(?=# |$)', content, re.DOTALL)
        if augment_section:
            for line in augment_section.group(1).split('\n'):
                if '|' in line and not line.strip().startswith('#'):
                    parts = line.split('|')
                    if len(parts) >= 2:
                        chinese_name = parts[1].strip()
                        if chinese_name:
                            existing_terms['augments'].add(chinese_name)
                            
    except Exception as e:
        print(f"解析术语表失败: {e}")
    
    return existing_terms

def extract_new_champions(data_file, existing_terms):
    """提取新的弈子术语"""
    data = load_json_file(data_file)
    if not data or 'data' not in data:
        return []
    
    new_champions = []
    for champion_data in data['data'].values():
        name = champion_data.get('name', '')
        if name and name not in existing_terms['champions']:
            # 排除一些特殊项目
            if name not in ['纹章之书', '成装锻造器', '基础装备锻造器', '神器装备锻造器', 
                           '辅助装锻造器', '赏金猎人宝箱', '测试消耗', '魔像', '峡谷迅捷蟹', '训练假人']:
                new_champions.append(name)
    
    return sorted(list(set(new_champions)))

def extract_new_traits(data_file, existing_terms):
    """提取新的羁绊术语"""
    data = load_json_file(data_file)
    if not data or 'data' not in data:
        return []
    
    new_traits = []
    for trait_data in data['data'].values():
        name = trait_data.get('name', '')
        if name and name not in existing_terms['traits']:
            new_traits.append(name)
    
    return sorted(list(set(new_traits)))

def extract_new_items(data_file, existing_terms):
    """提取新的装备术语"""
    data = load_json_file(data_file)
    if not data or 'data' not in data:
        return []
    
    new_items = []
    for item_data in data['data'].values():
        name = item_data.get('name', '')
        if name and name not in existing_terms['items']:
            # 排除一些特殊项目和变量名
            if (name and not name.startswith('@') and not name.startswith('tft_') 
                and not name.startswith('TFT') and not name.startswith('game_')
                and name not in ['小奖', '大奖', '头奖', '没中奖', '金箱', '金书', '金剑', '金袋', '金士兵', '金船', '金海妖', '金壳']
                and '件基础装备' not in name and '件成装' not in name and '颗战利品法球' not in name):
                new_items.append(name)
    
    return sorted(list(set(new_items)))

def extract_new_augments(data_file, existing_terms):
    """提取新的强化符文术语"""
    data = load_json_file(data_file)
    if not data or 'data' not in data:
        return []
    
    new_augments = []
    for augment_data in data['data'].values():
        name = augment_data.get('name', '')
        if name and name not in existing_terms['augments']:
            # 排除一些特殊项目
            if (name and not name.startswith('tft') and not name.startswith('TFT')
                and '强化果实移除器' not in name and '解锁强化' not in name):
                new_augments.append(name)
    
    return sorted(list(set(new_augments)))

def extract_charms_and_portals(others_file, mechanics_file):
    """提取强化果实和城邦相关术语"""
    charms = []
    portals = []
    
    # 从强化符文数据中提取强化果实相关术语
    augments_data = load_json_file('processed_data/tft_augments_zh_cn.json')
    if augments_data and 'data' in augments_data:
        for augment_data in augments_data['data'].values():
            name = augment_data.get('name', '')
            if '强化果实' in name:
                charms.append(name)
    
    # 从mechanics文件中提取
    mechanics_data = load_json_file(mechanics_file)
    if mechanics_data and 'data' in mechanics_data:
        for item_data in mechanics_data['data'].values():
            name = item_data.get('name', '')
            if name and name not in ['TFT15_SetMechanic_KeepCurrentOption_Name']:
                charms.append(name)
    
    # 从others文件中查找可能的城邦相关术语
    others_data = load_json_file(others_file)
    if others_data and 'data' in others_data:
        for item_data in others_data['data'].values():
            name = item_data.get('name', '')
            # 这里需要根据实际数据结构来判断哪些是城邦相关的
            # 暂时先收集所有非纹章的术语
            if (name and '纹章' not in name and not name.startswith('tft') 
                and not name.startswith('TFT') and not name.startswith('@')
                and name not in ['成装锻造器', '神器锻造器', '基础装备钥匙']):
                # 这些可能是城邦或其他特殊术语
                portals.append(name)
    
    return sorted(list(set(charms))), sorted(list(set(portals)))

def main():
    """主函数"""
    print("🔍 开始提取新术语...")
    
    # 文件路径
    prompt_file = 'processed_data/AI翻译prompt.txt'
    champions_file = 'processed_data/tft_champions_zh_cn.json'
    traits_file = 'processed_data/tft_traits_zh_cn.json'
    items_file = 'processed_data/tft_items_zh_cn.json'
    augments_file = 'processed_data/tft_augments_zh_cn.json'
    others_file = 'processed_data/tft_others_zh_cn.json'
    mechanics_file = 'processed_data/tft_mechanics_zh_cn.json'
    
    # 提取现有术语
    print("📖 分析现有术语表...")
    existing_terms = extract_existing_terms(prompt_file)
    print(f"   现有弈子: {len(existing_terms['champions'])} 个")
    print(f"   现有羁绊: {len(existing_terms['traits'])} 个")
    print(f"   现有装备: {len(existing_terms['items'])} 个")
    print(f"   现有强化符文: {len(existing_terms['augments'])} 个")
    
    # 提取新术语
    print("\n🆕 提取新术语...")
    new_champions = extract_new_champions(champions_file, existing_terms)
    new_traits = extract_new_traits(traits_file, existing_terms)
    new_items = extract_new_items(items_file, existing_terms)
    new_augments = extract_new_augments(augments_file, existing_terms)
    charms, portals = extract_charms_and_portals(others_file, mechanics_file)
    
    # 输出结果
    print(f"\n📊 提取结果:")
    print(f"   新弈子: {len(new_champions)} 个")
    print(f"   新羁绊: {len(new_traits)} 个")
    print(f"   新装备: {len(new_items)} 个")
    print(f"   新强化符文: {len(new_augments)} 个")
    print(f"   强化果实相关: {len(charms)} 个")
    print(f"   城邦相关: {len(portals)} 个")
    
    # 保存结果
    results = {
        'new_champions': new_champions,
        'new_traits': new_traits,
        'new_items': new_items,
        'new_augments': new_augments,
        'charms': charms,
        'portals': portals
    }
    
    with open('new_terms_extracted.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 结果已保存到 new_terms_extracted.json")
    
    # 显示部分结果预览
    if new_champions:
        print(f"\n🎭 新弈子预览 (前10个): {new_champions[:10]}")
    if new_traits:
        print(f"🔗 新羁绊预览 (前10个): {new_traits[:10]}")
    if new_items:
        print(f"⚔️ 新装备预览 (前10个): {new_items[:10]}")
    if new_augments:
        print(f"🔮 新强化符文预览 (前10个): {new_augments[:10]}")
    if charms:
        print(f"🍎 强化果实预览: {charms}")
    if portals:
        print(f"🏛️ 城邦相关预览 (前10个): {portals[:10]}")

if __name__ == "__main__":
    main()
